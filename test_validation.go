package main

import (
	"fmt"
	"arien/internal/security"
)

func main() {
	validator := security.NewValidator()
	apiKey := "sk-931c6a1daf1a4a299fea41bd5ac78e49"
	
	fmt.Printf("Testing API key: %s\n", apiKey)
	fmt.Printf("Length: %d\n", len(apiKey))
	fmt.Printf("Starts with 'sk-': %t\n", apiKey[:3] == "sk-")
	
	err := validator.ValidateAPIKey("deepseek", apiKey)
	if err != nil {
		fmt.Printf("Validation failed: %v\n", err)
	} else {
		fmt.Printf("Validation passed!\n")
	}
}
